<template>
  <div class="home-page">
    <!-- 我的任务统计卡片 -->
    <div class="task-cards">
      <div class="task-section">
        <h3 class="section-title">
          <el-icon><Bell /></el-icon>
          我的任务
        </h3>

        <div class="task-stats">
          <!-- 待我办理 -->
          <div class="task-card pending">
            <div class="card-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">待我办理</div>
              <div class="card-number">{{ taskStats.pending }}</div>
            </div>
          </div>

          <!-- 我已办理 -->
          <div class="task-card completed">
            <div class="card-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">我已办理</div>
              <div class="card-number">{{ taskStats.completed }}</div>
            </div>
          </div>

          <!-- 我发起的 -->
          <div class="task-card initiated">
            <div class="card-icon">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">我发起的</div>
              <div class="card-number">{{ taskStats.initiated }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="task-list-section">
        <div class="task-list-header">
          <span>序号</span>
          <span>操作</span>
          <span>流程类型</span>
          <span>流程名称</span>
          <span>发起人员</span>
          <span>流程状态</span>
          <span>接收时间</span>
          <span>发起时间</span>
        </div>

        <div class="no-data">
          <el-empty description="暂无数据" />
        </div>
      </div>
    </div>

    <!-- 统计图表区域 -->
    <div class="charts-section">
      <!-- 型号变更信息统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <el-icon><PieChart /></el-icon>
          <span>型号变更信息统计</span>
          <el-select v-model="selectedYear1" size="small" style="width: 80px;">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
          </el-select>
        </div>
        <div class="chart-content">
          <div class="chart-placeholder">
            <el-icon><PieChart /></el-icon>
            <span>图表区域</span>
          </div>
        </div>
      </div>

      <!-- 型号问题信息统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <el-icon><Histogram /></el-icon>
          <span>型号问题信息统计</span>
          <el-select v-model="selectedYear2" size="small" style="width: 80px;">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
          </el-select>
        </div>
        <div class="chart-content">
          <div class="chart-placeholder">
            <el-icon><Histogram /></el-icon>
            <span>图表区域</span>
          </div>
        </div>
      </div>

      <!-- 型号档案信息统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <el-icon><TrendCharts /></el-icon>
          <span>型号档案信息统计</span>
          <el-select v-model="selectedYear3" size="small" style="width: 80px;">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
          </el-select>
        </div>
        <div class="chart-content">
          <div class="chart-placeholder">
            <el-icon><TrendCharts /></el-icon>
            <span>图表区域</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  Bell,
  Clock,
  Check,
  Plus,
  PieChart,
  Histogram,
  TrendCharts
} from '@element-plus/icons-vue'

// 任务统计数据
const taskStats = reactive({
  pending: 0,
  completed: 0,
  initiated: 0
})

// 图表年份选择
const selectedYear1 = ref('2025')
const selectedYear2 = ref('2025')
const selectedYear3 = ref('2025')
</script>

<style scoped>
.home-page {
  padding: 0;
}

.task-cards {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.task-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.task-card {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.task-card:hover {
  transform: translateY(-2px);
}

.task-card.pending {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.task-card.completed {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.task-card.initiated {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.pending .card-icon {
  background: #2196f3;
  color: white;
}

.completed .card-icon {
  background: #4caf50;
  color: white;
}

.initiated .card-icon {
  background: #ff9800;
  color: white;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-number {
  font-size: 32px;
  font-weight: 600;
  color: #333;
}

.task-list-section {
  margin-top: 20px;
}

.task-list-header {
  display: grid;
  grid-template-columns: 60px 80px 120px 200px 120px 120px 150px 150px;
  gap: 16px;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 4px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.no-data {
  padding: 40px;
  text-align: center;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.chart-header span {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-left: 8px;
}

.chart-content {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #ccc;
  font-size: 48px;
}

.chart-placeholder span {
  font-size: 14px;
}
</style>
